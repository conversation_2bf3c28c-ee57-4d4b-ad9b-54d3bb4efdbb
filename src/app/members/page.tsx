'use client';
import { useState } from 'react';

const dataSource = [
  { key: '1', name: '张三', phone: '13800000000', registerTime: '2024-06-01', balance: 200 },
  { key: '2', name: '李四', phone: '13900000000', registerTime: '2024-06-02', balance: 150 },
  // ...更多数据
];

export default function MemberList() {
  const [search, setSearch] = useState('');
  const filteredData = dataSource.filter(item =>
    item.name.includes(search) || item.phone.includes(search)
  );

  return (
    <div className="min-h-screen bg-gray-50 py-10">
      <div className="max-w-4xl mx-auto bg-white rounded-2xl shadow p-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-green-500">会员列表</h1>
          <button className="bg-green-300 hover:bg-green-400 text-white px-4 py-2 rounded-lg shadow transition">
            新增会员
          </button>
        </div>
        <div className="mb-4 flex justify-end">
          <input
            className="border border-gray-300 rounded px-3 py-2 w-60 focus:outline-none focus:ring-2 focus:ring-green-200"
            placeholder="搜索姓名或手机号"
            value={search}
            onChange={e => setSearch(e.target.value)}
          />
        </div>
        <div className="overflow-x-auto">
          <table className="w-full text-left border-collapse">
            <thead>
              <tr className="bg-gray-100">
                <th className="py-2 px-4">姓名</th>
                <th className="py-2 px-4">手机号</th>
                <th className="py-2 px-4">注册时间</th>
                <th className="py-2 px-4">余额</th>
                <th className="py-2 px-4">操作</th>
              </tr>
            </thead>
            <tbody>
              {filteredData.map(item => (
                <tr key={item.key} className="hover:bg-green-50 transition">
                  <td className="py-2 px-4">{item.name}</td>
                  <td className="py-2 px-4">{item.phone}</td>
                  <td className="py-2 px-4">{item.registerTime}</td>
                  <td className="py-2 px-4 text-green-600 font-semibold">¥{item.balance}</td>
                  <td className="py-2 px-4">
                    <button className="text-blue-500 hover:underline mr-2">详情</button>
                    <button className="text-yellow-500 hover:underline">编辑</button>
                  </td>
                </tr>
              ))}
              {filteredData.length === 0 && (
                <tr>
                  <td colSpan={5} className="text-center text-gray-400 py-8">暂无数据</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
} 