# 会员系统前端开发文档（Next.js + WindiCSS）

---

## 一、技术选型

- 框架：Next.js
- 样式：WindiCSS（原子化 CSS 框架，类似 Tailwind CSS）
- 包管理：Yarn

---

## 二、依赖安装（使用 Yarn）

1. 初始化 Next.js 项目（如未初始化）
···bash
nvm use 20.16.0
```

```bash
yarn create next-app --typescript
```

2. 安装 WindiCSS 及相关依赖

```bash
yarn add windicss vite-plugin-windicss @windicss/plugin-utils
```

3. 安装 next-windicss 插件（推荐社区方案）

```bash
yarn add next-windicss
```

---

## 三、WindiCSS 集成步骤

1. 在项目根目录创建 `next.config.js`，引入 next-windicss 插件：

```js
const withWindiCSS = require('next-windicss');
module.exports = withWindiCSS({});
```

2. 在项目根目录新建 `windi.config.ts`（可选，做自定义配置）

3. 在 `pages/_app.tsx` 中引入 WindiCSS 样式：

```tsx
import 'windi.css';
import type { AppProps } from 'next/app';

export default function App({ Component, pageProps }: AppProps) {
  return <Component {...pageProps} />;
}
```

---

## 四、目录结构建议

```
/pages
  /members
    index.tsx           // 会员列表页
    new.tsx             // 新增会员页
    [id]/
      index.tsx         // 会员详情页
      edit.tsx          // 编辑会员页
      recharge.tsx      // 充值页
      consume.tsx       // 消费页
/statistics
  index.tsx             // 统计分析页
/components
  MemberTable.tsx       // 会员表格组件
  MemberForm.tsx        // 会员表单组件
  RecordTable.tsx       // 记录表格组件
  StatisticsChart.tsx   // 统计图表组件
  ...
```

---

## 五、页面结构与风格建议

### 1. 页面结构

- 会员列表页：展示会员信息、搜索、新增、操作按钮
- 会员详情页：展示会员基本信息、充值/消费入口、记录列表
- 新增/编辑会员页：表单填写会员信息
- 充值/消费页：表单填写金额、内容
- 统计分析页：统计类型选择、时间范围、图表与数据表

### 2. 风格建议

- 主色调：浅绿色（如 #6EE7B7）、白色、浅灰色，点缀色可用淡蓝或橙色
- 字体：无衬线字体，字号适中，行距舒适
- 布局：留白充足，卡片式分区，圆角、阴影适度
- 按钮/输入框：圆角、轻微阴影、悬浮时有色彩变化
- 图表：色彩柔和，线条简洁，数据标签清晰
- 响应式：适配桌面和移动端

---

## 六、会员列表页页面代码示例（pages/members/index.tsx）

```tsx
import { useState } from 'react';

const dataSource = [
  { key: '1', name: '张三', phone: '13800000000', registerTime: '2024-06-01', balance: 200 },
  { key: '2', name: '李四', phone: '13900000000', registerTime: '2024-06-02', balance: 150 },
  // ...更多数据
];

export default function MemberList() {
  const [search, setSearch] = useState('');
  const filteredData = dataSource.filter(item =>
    item.name.includes(search) || item.phone.includes(search)
  );

  return (
    <div className="min-h-screen bg-gray-50 py-10">
      <div className="max-w-4xl mx-auto bg-white rounded-2xl shadow p-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-green-500">会员列表</h1>
          <button className="bg-green-300 hover:bg-green-400 text-white px-4 py-2 rounded-lg shadow transition">
            新增会员
          </button>
        </div>
        <div className="mb-4 flex justify-end">
          <input
            className="border border-gray-300 rounded px-3 py-2 w-60 focus:outline-none focus:ring-2 focus:ring-green-200"
            placeholder="搜索姓名或手机号"
            value={search}
            onChange={e => setSearch(e.target.value)}
          />
        </div>
        <div className="overflow-x-auto">
          <table className="w-full text-left border-collapse">
            <thead>
              <tr className="bg-gray-100">
                <th className="py-2 px-4">姓名</th>
                <th className="py-2 px-4">手机号</th>
                <th className="py-2 px-4">注册时间</th>
                <th className="py-2 px-4">余额</th>
                <th className="py-2 px-4">操作</th>
              </tr>
            </thead>
            <tbody>
              {filteredData.map(item => (
                <tr key={item.key} className="hover:bg-green-50 transition">
                  <td className="py-2 px-4">{item.name}</td>
                  <td className="py-2 px-4">{item.phone}</td>
                  <td className="py-2 px-4">{item.registerTime}</td>
                  <td className="py-2 px-4 text-green-600 font-semibold">¥{item.balance}</td>
                  <td className="py-2 px-4">
                    <button className="text-blue-500 hover:underline mr-2">详情</button>
                    <button className="text-yellow-500 hover:underline">编辑</button>
                  </td>
                </tr>
              ))}
              {filteredData.length === 0 && (
                <tr>
                  <td colSpan={5} className="text-center text-gray-400 py-8">暂无数据</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
```

---

## 七、其他说明

- 其他页面（如会员详情页、统计页等）可参考上述风格和结构实现。
- 如需自定义主题或扩展 WindiCSS 功能，可在 `windi.config.ts` 中配置。
- 推荐使用 VSCode 插件 [WindiCSS IntelliSense] 提升开发体验。

---

如需更多页面代码或组件示例，请随时补充需求！ 